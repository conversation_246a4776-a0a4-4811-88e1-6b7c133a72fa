import React, { useState } from 'react';

const DID_API_URL = 'https://api.d-id.com/talks';

const ChatBot: React.FC = () => {
  const [text, setText] = useState<string>('');
  const [videoUrl, setVideoUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  const createTalk = async () => {
    setLoading(true);
    setVideoUrl(null);

    const payload = {
      script: {
        type: 'text',
        input: text,
        // You can add voice_id, provider, style, etc.
      },
      // source_url: 'https://your-avatar-url.png', // Optional
    };

    try {
      const response = await fetch(DID_API_URL, {
        method: 'POST',
        headers: {
          Authorization: `Basic ${btoa(`${import.meta.env.VITE_DID_API_KEY}`)}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`Failed to create talk: ${response.statusText}`);
      }

      const data = await response.json();
      const talkId = data.id;

      let videoUrlResponse: string | null = null;

      while (!videoUrlResponse) {
        const pollResp = await fetch(`${DID_API_URL}/${talkId}`, {
          headers: {
            Authorization: `Basic ${btoa(`${import.meta.env.VITE_DID_API_KEY}:${import.meta.env.VITE_DID_API_SECRET}`)}`,
            'Content-Type': 'application/json',
          },
        });

        const pollData = await pollResp.json();

        if (pollData.status === 'done' && pollData.result_url) {
          videoUrlResponse = pollData.result_url;
        } else {
          await new Promise((res) => setTimeout(res, 3000));
        }
      }

      setVideoUrl(videoUrlResponse);
    } catch (err: any) {
      console.error(err);
      alert('Error generating video: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (text.trim() === '') return;
    createTalk();
  };

  return (
    <div className="max-w-xl mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">AI Speaking Bot</h1>
      <form onSubmit={handleSubmit}>
        <textarea
          className="w-full border rounded p-2 mb-3"
          rows={4}
          value={text}
          onChange={(e) => setText(e.target.value)}
          placeholder="Type your message here..."
        />
        <button
          type="submit"
          className="bg-blue-500 text-white py-2 px-4 rounded"
          disabled={loading}
        >
          {loading ? 'Processing...' : 'Speak'}
        </button>
      </form>
      {videoUrl && (
        <div className="mt-6">
          <h2 className="text-xl font-semibold mb-2">Avatar Speaking:</h2>
          <video src={videoUrl} controls autoPlay className="w-full border rounded" />
        </div>
      )}
    </div>
  );
};

export default ChatBot;
